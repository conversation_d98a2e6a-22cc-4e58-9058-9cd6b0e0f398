<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getPracticeDetail, updatePracticeProgress } from '@/api/exam'
import type { PracticeResult } from '@/types/api/Exam'

const practiceDetail = ref<PracticeResult | null>(null)
const currentQuestionIndex = ref(0)
const userAnswers = ref<Record<string, any>>({})
const loading = ref(false)
const practiceId = ref<number>()

const autoSaveTimer = ref<any>(null)

// 页面加载时获取参数
onLoad((options: any) => {
  if (options.practiceId) {
    practiceId.value = Number(options.practiceId)
    fetchPracticeDetail()
  }
})

// 获取练习详情
const fetchPracticeDetail = async () => {
  if (!practiceId.value) return

  try {
    loading.value = true
    const result = await getPracticeDetail({ practiceId: practiceId.value })
    practiceDetail.value = result
    startAutoSave() // 开始自动保存
  } catch (error) {
    console.error('获取练习详情失败:', error)
    uni.showToast({
      title: '获取练习详情失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 当前题目
const currentQuestion = computed(() => {
  if (!practiceDetail.value?.questions) return null
  return practiceDetail.value.questions[currentQuestionIndex.value]
})

// 答题统计
const answeredCount = computed(() => {
  if (!practiceDetail.value?.questions) return 0
  return practiceDetail.value.questions.filter((_, index) => isQuestionAnswered(index)).length
})

// 自动保存功能
const startAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
  }
  autoSaveTimer.value = setInterval(() => {
    saveProgress()
  }, 30000) // 每30秒自动保存一次
}

// 监听答案变化，触发自动保存
watch(userAnswers, () => {
  // 延迟保存，避免频繁调用
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }
  autoSaveTimer.value = setTimeout(() => {
    saveProgress()
  }, 2000) // 2秒后保存
}, { deep: true })

// 页面卸载时清理定时器
onUnmounted(() => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
    clearTimeout(autoSaveTimer.value)
  }
})

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'SINGLE':
      return '单选题'
    case 'MULTIPLE':
      return '多选题'
    default:
      return type
  }
}

// 选择答案
const selectAnswer = (optionKey: string) => {
  if (!currentQuestion.value) return

  const questionId = currentQuestion.value.id.toString()

  if (currentQuestion.value.type === 'MULTIPLE_CHOICE') {
    // 多选题
    if (!userAnswers.value[questionId]) {
      userAnswers.value[questionId] = []
    }
    const answers = userAnswers.value[questionId] as string[]
    const index = answers.indexOf(optionKey)
    if (index > -1) {
      answers.splice(index, 1)
    } else {
      answers.push(optionKey)
    }
  } else {
    // 单选题
    userAnswers.value[questionId] = optionKey
  }
}

// 检查选项是否被选中
const isOptionSelected = (optionKey: string) => {
  if (!currentQuestion.value) return false

  const questionId = currentQuestion.value.id.toString()
  const answer = userAnswers.value[questionId]

  if (currentQuestion.value.type === 'MULTIPLE_CHOICE') {
    return Array.isArray(answer) && answer.includes(optionKey)
  } else {
    return answer === optionKey
  }
}

// 上一题
const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

// 下一题
const nextQuestion = () => {
  if (practiceDetail.value && currentQuestionIndex.value < practiceDetail.value.questions.length - 1) {
    currentQuestionIndex.value++
  }
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  currentQuestionIndex.value = index
}

// 检查题目是否已答
const isQuestionAnswered = (index: number) => {
  if (!practiceDetail.value?.questions) return false
  const question = practiceDetail.value.questions[index]
  const questionId = question.id.toString()
  const answer = userAnswers.value[questionId]

  if (question.type === 'MULTIPLE_CHOICE') {
    return Array.isArray(answer) && answer.length > 0
  } else {
    return answer !== undefined && answer !== null && answer !== ''
  }
}

// 保存答题进度
const saveProgress = async () => {
  if (!practiceId.value) return

  try {
    await updatePracticeProgress({
      practiceId: practiceId.value,
      answerData: userAnswers.value
    })
  } catch (error) {
    console.error('保存进度失败:', error)
  }
}

// 提交答案
const submitAnswers = async () => {
  uni.showModal({
    title: '确认提交',
    content: '确定要提交答案吗？提交后将无法修改。',
    success: async (res) => {
      if (res.confirm) {
        await saveProgress()
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        })
        // 返回练习详情页
        uni.navigateBack()
      }
    }
  })
}

// 显示题目列表弹窗
const showQuestionList = ref(false)

const toggleQuestionList = () => {
  showQuestionList.value = !showQuestionList.value
}


</script>

<template>
  <view class="practice-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习内容 -->
    <view v-else-if="practiceDetail && currentQuestion" class="practice-content">
      <!-- 进度信息 -->
      <view class="progress-section">
        <view class="progress-info">
          <view class="question-info">
            <text class="question-label">题目：</text>
            <text class="question-number">{{ currentQuestionIndex + 1 }}/{{ practiceDetail.questions.length }}</text>
          </view>
          <view class="answer-stats">
            <text class="stats-text">已答：{{ answeredCount }}/{{ practiceDetail.questions.length }}</text>
          </view>
        </view>
        <view class="progress-bar-container">
          <view class="progress-bar-track">
            <view
              class="progress-bar-fill"
              :style="{ width: `${(answeredCount / practiceDetail.questions.length) * 100}%` }"
            ></view>
          </view>
          <view class="progress-text">
            <text>答题进度：{{ Math.round((answeredCount / practiceDetail.questions.length) * 100) }}%</text>
          </view>
        </view>
      </view>

      <!-- 题目卡片 -->
      <view class="question-card">
        <view class="question-header">
          <text class="question-type">问题{{currentQuestionIndex + 1}}: {{ getQuestionTypeText(currentQuestion.type) }}</text>
        </view>
        <view class="question-content">
          <text class="question-title">{{ currentQuestion.title }}</text>
          <view v-if="currentQuestion.content" class="question-description">
            <text>{{ currentQuestion.content }}</text>
          </view>
        </view>

        <!-- 选项列表 -->
        <view class="options-container">
          <view
            v-for="option in currentQuestion.options"
            :key="option.id"
            class="option-item"
            :class="{ 'option-selected': isOptionSelected(option.optionKey) }"
            @click="selectAnswer(option.optionKey)"
          >
            <view
              class="option-indicator"
              :class="{
                'selected': isOptionSelected(option.optionKey),
                'checkbox': currentQuestion.type === 'MULTIPLE_CHOICE',
                'radio': currentQuestion.type !== 'MULTIPLE_CHOICE'
              }"
            >
              <!-- 多选题显示复选框 -->
              <text
                v-if="currentQuestion.type === 'MULTIPLE_CHOICE'"
                class="checkbox-icon"
                :class="{ 'checked': isOptionSelected(option.optionKey) }"
              >
                {{ isOptionSelected(option.optionKey) ? '✓' : '' }}
              </text>
              <!-- 单选题显示选项字母 -->
              <text
                v-else
                class="option-key"
              >
                {{ option.optionKey }}
              </text>
            </view>
            <text class="option-text">{{ option.content }}</text>
          </view>
        </view>
      </view>

      <!-- 导航按钮 -->
      <view class="navigation-section">
        <view class="nav-buttons">
          <button
            class="nav-btn prev-btn"
            :disabled="currentQuestionIndex === 0"
            @click="previousQuestion"
          >
            上一题
          </button>
          <button class="nav-btn list-btn" @click="toggleQuestionList">
            题目列表
          </button>
          <button
            class="nav-btn next-btn"
            v-if="currentQuestionIndex < practiceDetail.questions.length - 1"
            @click="nextQuestion"
          >
            下一题
          </button>
          <button
            class="nav-btn submit-btn"
            v-else
            @click="submitAnswers"
          >
            提交答案
          </button>
        </view>
      </view>
    </view>

    <!-- 题目列表弹窗 -->
    <view v-if="showQuestionList" class="question-list-modal" @click="toggleQuestionList">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">题目列表</text>
          <text class="close-btn" @click="toggleQuestionList">×</text>
        </view>
        <view class="questions-grid">
          <view
            v-for="(question, index) in practiceDetail?.questions || []"
            :key="question.id"
            class="question-grid-item"
            :class="{
              'question-answered': isQuestionAnswered(index),
              'question-current': index === currentQuestionIndex
            }"
            @click="jumpToQuestion(index); toggleQuestionList()"
          >
            <text>{{ index + 1 }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-container">
      <text class="empty-text">练习内容不存在</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "练习"
  }
}
</route>

<style scoped lang="scss">
.practice-page {
  background-color: #f7f7f5;
  min-height: 100vh;
  padding-bottom: 80px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.practice-content {
  padding: 16px;
}

.progress-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.question-info {
  display: flex;
  align-items: center;
}

.question-label {
  font-size: 14px;
  color: #666;
}

.question-number {
  font-size: 16px;
  color: #1890ff;
  font-weight: 600;
}

.answer-stats {
  display: flex;
  align-items: center;
}

.stats-text {
  font-size: 12px;
  color: #52c41a;
  background-color: #f6ffed;
  padding: 2px 6px;
  border-radius: 4px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.progress-bar-container {
  width: 100%;
}

.progress-bar-track {
  height: 6px;
  background-color: #e6f7ff;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: #1890ff;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.question-card {
  background-color: #ffffff;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.question-header {
  background-color: #f0f7ff;
  padding: 12px 16px;
  border-bottom: 1px solid #e6f7ff;
}

.question-type {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.question-content {
  padding: 16px;
}

.question-title {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.question-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.options-container {
  padding: 0 16px 16px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #ffffff;
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.option-selected {
    border-color: #1890ff;
    background-color: #f0f7ff;
  }
}

.option-indicator {
  width: 24px;
  height: 24px;
  border: 2px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  transition: all 0.2s ease;

  &.radio {
    border-radius: 50%;

    &.selected {
      border-color: #1890ff;
      background-color: #1890ff;
    }
  }

  &.checkbox {
    border-radius: 4px;

    &.selected {
      border-color: #1890ff;
      background-color: #1890ff;
    }
  }
}

.option-key {
  font-size: 12px;
  font-weight: 500;
  color: #666;

  .selected & {
    color: white;
  }
}

.checkbox-icon {
  font-size: 14px;
  font-weight: bold;
  color: white;

  &.checked {
    color: white;
  }
}

.option-text {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.navigation-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

.nav-buttons {
  display: flex;
  gap: 8px;
}

.nav-btn {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.prev-btn, .next-btn {
  background-color: #f5f5f5;
  color: #333;

  &:active:not(:disabled) {
    background-color: #e8e8e8;
  }
}

.list-btn {
  background-color: #e6f7ff;
  color: #1890ff;

  &:active {
    background-color: #bae7ff;
  }
}

.submit-btn {
  background-color: #1890ff;
  color: white;

  &:active {
    background-color: #096dd9;
  }
}

.question-list-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #ffffff;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.questions-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.question-grid-item {
  width: 40px;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;

  &.question-answered {
    background-color: #f6ffed;
    border-color: #52c41a;
    color: #52c41a;
  }

  &.question-current {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.empty-text {
  font-size: 16px;
  color: #666;
}
</style>
