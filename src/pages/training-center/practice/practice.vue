<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useToast, useMessage } from 'wot-design-uni'
import { getPracticeDetail, updatePracticeProgress } from '@/api/exam'
import type { PracticeResult } from '@/types/api/Exam'

const toast = useToast()
const message = useMessage()

const practiceDetail = ref<PracticeResult | null>(null)
const currentQuestionIndex = ref(0)
const userAnswers = ref<Record<string, any>>({})
const loading = ref(false)
const practiceId = ref<number>()

// 页面加载时获取参数
onLoad((options: any) => {
  if (options.practiceId) {
    practiceId.value = Number(options.practiceId)
    fetchPracticeDetail()
  }
})

// 获取练习详情
const fetchPracticeDetail = async () => {
  if (!practiceId.value) {
    console.error('练习ID不存在')
    toast.error('练习ID不存在')
    return
  }

  try {
    loading.value = true
    console.log('正在获取练习详情，practiceId:', practiceId.value)
    const result = await getPracticeDetail({ practiceId: practiceId.value })
    console.log('获取到的练习详情:', result)

    if (!result) {
      console.error('练习详情为空')
      toast.error('练习详情为空')
      return
    }

    if (!result.questions || result.questions.length === 0) {
      console.error('练习题目为空')
      toast.error('练习题目为空')
      return
    }

    practiceDetail.value = result
  } catch (error) {
    console.error('获取练习详情失败:', error)
    toast.error('获取练习详情失败')
  } finally {
    loading.value = false
  }
}

// 当前题目
const currentQuestion = computed(() => {
  if (!practiceDetail.value?.questions) return null
  return practiceDetail.value.questions[currentQuestionIndex.value]
})

// 答题统计
const answeredCount = computed(() => {
  if (!practiceDetail.value?.questions) return 0
  return practiceDetail.value.questions.filter((_, index) => isQuestionAnswered(index)).length
})



// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'SINGLE':
      return '单选题'
    case 'MULTIPLE':
      return '多选题'
    default:
      return type
  }
}

// 是否显示答案解析
const showAnalysis = ref(false)
// 记录已确认答案的题目ID
const confirmedQuestions = ref<Set<string>>(new Set())

// 当前题目是否已确认答案
const isCurrentQuestionConfirmed = computed(() => {
  if (!currentQuestion.value) return false
  const questionId = currentQuestion.value.id.toString()
  return confirmedQuestions.value.has(questionId)
})

// 选择答案
const selectAnswer = (optionKey: string) => {
  if (!currentQuestion.value || isCurrentQuestionConfirmed.value) return

  const questionId = currentQuestion.value.id.toString()

  if (currentQuestion.value.type === 'MULTIPLE') {
    // 多选题
    if (!userAnswers.value[questionId]) {
      userAnswers.value[questionId] = []
    }
    const answers = userAnswers.value[questionId] as string[]
    const index = answers.indexOf(optionKey)
    if (index > -1) {
      answers.splice(index, 1)
    } else {
      answers.push(optionKey)
    }
  } else {
    // 单选题
    userAnswers.value[questionId] = optionKey
  }
}

// 确认答案
const confirmAnswer = () => {
  if (!currentQuestion.value) return

  const questionId = currentQuestion.value.id.toString()
  const answer = userAnswers.value[questionId]

  // 检查是否已选择答案
  if (!answer || (Array.isArray(answer) && answer.length === 0)) {
    toast.warning('请先选择答案')
    return
  }

  // 确认答案并显示解析
  confirmedQuestions.value.add(questionId)
  showAnalysis.value = true

  // 更新进度
  updateProgress()
}

// 检查选项是否被选中
const isOptionSelected = (optionKey: string) => {
  if (!currentQuestion.value) return false

  const questionId = currentQuestion.value.id.toString()
  const answer = userAnswers.value[questionId]

  if (currentQuestion.value.type === 'MULTIPLE') {
    return Array.isArray(answer) && answer.includes(optionKey)
  } else {
    return answer === optionKey
  }
}

// 上一题
const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

// 下一题
const nextQuestion = () => {
  if (practiceDetail.value) {
    if (currentQuestionIndex.value < practiceDetail.value.questions.length - 1) {
      currentQuestionIndex.value++
    } else {
      // 最后一题，提交答案
      submitAnswers()
    }
  }
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  currentQuestionIndex.value = index
}

// 检查题目是否已答
const isQuestionAnswered = (index: number) => {
  if (!practiceDetail.value?.questions) return false
  const question = practiceDetail.value.questions[index]
  const questionId = question.id.toString()
  const answer = userAnswers.value[questionId]

  if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE') {
    return Array.isArray(answer) && answer.length > 0
  } else {
    return answer !== undefined && answer !== null && answer !== ''
  }
}

// 保存答题进度
const saveProgress = async () => {
  if (!practiceId.value) return

  try {
    await updatePracticeProgress({
      practiceId: practiceId.value,
      answerData: userAnswers.value,
    })
  } catch (error) {
    console.error('保存进度失败:', error)
  }
}

// 提交答案
const submitAnswers = async () => {
  uni.showModal({
    title: '确认提交',
    content: '确定要提交答案吗？提交后将无法修改。',
    success: async res => {
      if (res.confirm) {
        await saveProgress()
        // 跳转到练习完成页面
        uni.redirectTo({
          url: `/pages/training-center/practice/complete?practiceId=${practiceId.value}`,
        })
      }
    },
  })
}

// 显示题目列表弹窗
const showQuestionList = ref(false)

const toggleQuestionList = () => {
  showQuestionList.value = !showQuestionList.value
}

// 返回上一页
const handleGoBack = () => {
  uni.navigateBack()
}

// 获取正确答案文本
const getCorrectOptionText = (question: any) => {
  if (!question || !question.answer || !question.options) return ''
  const correctOption = question.options.find((opt: any) => opt.optionKey === question.answer)
  return correctOption?.content ?? ''
}

// 检查用户答案是否正确
const isUserAnswerCorrect = (question: any) => {
  if (!question || !question.answer) return false

  const questionId = question.id.toString()
  const userAnswer = userAnswers.value[questionId]

  if (!userAnswer) return false

  if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE') {
    // 多选题：比较排序后的答案
    if (!Array.isArray(userAnswer)) return false
    const sortedUserAnswer = userAnswer.sort().join(',')
    const sortedCorrectAnswer = question.answer.split(',').sort().join(',')
    return sortedUserAnswer === sortedCorrectAnswer
  } else {
    // 单选题：直接比较
    return userAnswer === question.answer
  }
}

// 切换题目时根据确认状态决定是否显示解析
watch(currentQuestionIndex, () => {
  if (currentQuestion.value) {
    const questionId = currentQuestion.value.id.toString()
    showAnalysis.value = confirmedQuestions.value.has(questionId)
  } else {
    showAnalysis.value = false
  }
})
</script>

<template>
  <view class="practice-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习内容 -->
    <view v-else-if="practiceDetail && currentQuestion" class="practice-content">
      <!-- 进度信息 -->
      <view class="progress-section">
        <view class="progress-info">
          <view class="question-count">
            <text class="label">题目：</text>
            <text class="value"
              >{{ currentQuestionIndex + 1 }}/{{ practiceDetail.questions.length }}</text
            >
          </view>
        </view>
        <view class="progress-bar">
          <view
            class="progress-fill"
            :style="{ width: `${(answeredCount / practiceDetail.questions.length) * 100}%` }"
          ></view>
        </view>
      </view>

      <!-- 题目卡片 -->
      <view class="question-card">
        <view class="question-header">
          <text
            >问题 {{ currentQuestionIndex + 1 }}：{{
              getQuestionTypeText(currentQuestion.type)
            }}</text
          >
        </view>

        <view class="question-content">
          <text>{{ currentQuestion.title }}</text>
        </view>

        <view class="options-list">
          <view
            v-for="option in currentQuestion?.options || []"
            :key="option.id"
            class="option"
            :class="{
              selected: isOptionSelected(option.optionKey),
              disabled: isCurrentQuestionConfirmed,
            }"
            @click="selectAnswer(option.optionKey)"
          >
            <view
              class="option-indicator"
              :class="{ selected: isOptionSelected(option.optionKey) }"
            >
              <text>{{ option.optionKey }}</text>
            </view>
            <text class="option-text">{{ option.content }}</text>
          </view>
        </view>

        <!-- 确认答案按钮 -->
        <view v-if="!isCurrentQuestionConfirmed" class="confirm-section">
          <wd-button type="primary" :round="false" @click="confirmAnswer"> 确认答案 </wd-button>
        </view>

        <!-- 答案解析 -->
        <view v-if="showAnalysis" class="answer-analysis">
          <!-- 答题结果反馈 -->
          <view
            class="feedback-badge"
            :class="{
              'feedback-correct': isUserAnswerCorrect(currentQuestion),
              'feedback-wrong': !isUserAnswerCorrect(currentQuestion),
            }"
          >
            <text class="feedback-icon">
              {{ isUserAnswerCorrect(currentQuestion) ? '✓' : '✗' }}
            </text>
            <text class="feedback-text">
              {{ isUserAnswerCorrect(currentQuestion) ? '回答正确' : '回答错误' }}
            </text>
          </view>

          <text class="analysis-title">答案解析</text>
          <text class="analysis-content"
            >正确答案：{{ currentQuestion.answer }} -
            {{ getCorrectOptionText(currentQuestion) }}</text
          >
          <text v-if="currentQuestion.analysis" class="analysis-detail">{{
            currentQuestion.analysis
          }}</text>
        </view>
      </view>

      <!-- 底部导航 -->
      <view class="navigation-bar">
        <view
          class="nav-btn nav-btn--prev"
          :class="{ disabled: currentQuestionIndex === 0 }"
          @click="previousQuestion"
        >
          <view class="nav-btn__icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="8"
              height="14"
              viewBox="0 0 8 14"
              fill="none"
            >
              <path
                d="M7.14197 13.7985C6.98751 13.7978 6.83961 13.7359 6.73072 13.6264L0.230721 7.1264C0.00454944 6.89888 0.00454944 6.53143 0.230721 6.3039L6.73072 0.203903C6.95181 -0.0542651 7.34592 -0.0694876 7.58626 0.170858C7.82661 0.411202 7.81139 0.805313 7.55322 1.0264L1.46739 4.11515L7.55322 7.2039C7.71902 7.37069 7.76845 7.62073 7.67859 7.83806C7.58872 8.05539 7.37714 8.1975 7.14197 8.19849L7.14197 13.7985Z"
                fill="#1677FF"
              />
            </svg>
          </view>
          <text>上一题</text>
        </view>

        <view class="nav-btn nav-btn--list" @click="toggleQuestionList">
          <text>题目列表</text>
        </view>

        <view class="nav-btn nav-btn--next" @click="nextQuestion">
          <text>{{
            currentQuestionIndex === practiceDetail.questions.length - 1 ? '提交答案' : '下一题'
          }}</text>
          <view
            v-if="currentQuestionIndex < practiceDetail.questions.length - 1"
            class="nav-btn__icon"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="8"
              height="14"
              viewBox="0 0 8 14"
              fill="none"
            >
              <path
                d="M0.858031 13.7985C1.01249 13.7978 1.16039 13.7359 1.26928 13.6264L7.76928 7.1264C7.99545 6.89888 7.99545 6.53143 7.76928 6.3039L1.26928 0.203903C1.04819 -0.0542651 0.654082 -0.0694876 0.413736 0.170858C0.173391 0.411202 0.188613 0.805313 0.446784 1.0264L6.53261 4.11515L0.446784 7.2039C0.280983 7.37069 0.231553 7.62073 0.321417 7.83806C0.411282 8.05539 0.622863 8.1975 0.858031 8.19849L0.858031 13.7985Z"
                fill="#FFFFFF"
              />
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <text class="empty-text">练习内容不存在</text>
      <text class="empty-desc">请检查练习ID是否正确或联系管理员</text>
      <view class="empty-actions">
        <button class="back-btn" @click="handleGoBack">返回</button>
      </view>
    </view>

    <!-- 题目列表弹窗 -->
    <view v-if="showQuestionList" class="modal-overlay" @click="toggleQuestionList">
      <view class="modal" @click.stop>
        <view class="modal__header">
          <text class="modal__title">题目列表</text>
          <view class="modal__close" @click="toggleQuestionList">
            <text>×</text>
          </view>
        </view>

        <view class="modal__body">
          <!-- 状态说明 -->
          <view class="status-legend">
            <text class="legend__title">状态说明</text>
            <view class="legend__items">
              <view class="legend__item">
                <view class="legend__indicator legend__indicator--answered"></view>
                <text class="legend__text">已答题</text>
              </view>
              <view class="legend__item">
                <view class="legend__indicator legend__indicator--current"></view>
                <text class="legend__text">当前题</text>
              </view>
            </view>
          </view>

          <!-- 全部题目 -->
          <view class="questions-section">
            <text class="section__title">全部题目</text>
            <view class="questions-grid">
              <view
                v-for="(question, index) in practiceDetail?.questions || []"
                :key="question.id"
                class="question-grid-item"
                :class="{
                  'question-grid-item--answered': isQuestionAnswered(index),
                  'question-grid-item--current': index === currentQuestionIndex,
                }"
                @click="
                  jumpToQuestion(index)
                  toggleQuestionList()
                "
              >
                <text>{{ index + 1 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "练习"
  }
}
</route>

<style scoped lang="scss">
// 设计令牌
$colors: (
  primary: #1677ff,
  primary-light: #f0f7ff,
  secondary: #37acfe,
  success: #52c41a,
  success-light: #f6ffed,
  text-primary: #333333,
  text-secondary: #666666,
  text-tertiary: #999999,
  bg-page: #f7f7f5,
  bg-card: #ffffff,
  bg-light: #f5f7fa,
  border-light: #e8e8e8,
  border-medium: #d9d9d9,
  border-primary: #d6e8ff,
  white: #ffffff,
);

@function color($name) {
  @return map-get($colors, $name);
}

// 基础布局
.practice-page {
  background-color: color(bg-page);
  min-height: 100vh;
  padding-bottom: 95px;
}

.practice-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: color(text-secondary);
}

.loading-text {
  color: color(text-secondary);
  font-size: 14px;
}

// 进度区域
.progress-section {
  background-color: color(bg-card);
  padding: 12px 18px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: color(text-secondary);
  margin-bottom: 10px;
}

.question-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.label {
  color: color(text-secondary);
}

.value {
  color: color(primary);
}

.progress-bar {
  height: 2px;
  background-color: color(primary-light);
  border-radius: 1px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: color(primary);
  border-radius: 1px;
  transition: width 0.3s ease;
}

// 题目卡片
.question-card {
  background-color: color(bg-card);
  margin: 0 16px;
  border-radius: 8px;
  overflow: hidden;
}

.question-header {
  background-color: color(primary-light);
  padding: 8px 16px;
  font-size: 15px;
  color: color(primary);
  font-weight: 500;
}

.question-content {
  padding: 15px 16px;
  font-size: 15px;
  color: color(text-primary);
  line-height: 1.5;
}

.options-list {
  padding: 0 16px 20px;
}

.option {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid color(border-light);
  border-radius: 6px;
  margin-bottom: 10px;
  background-color: color(bg-card);
  transition: all 0.2s ease;
  cursor: pointer;

  &.selected {
    border-color: color(primary);
    background-color: color(primary-light);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:hover:not(.disabled) {
    border-color: color(primary);
  }
}

.option-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 1px solid color(border-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 14px;
  color: color(text-secondary);
  transition: all 0.2s ease;

  &.selected {
    border-color: color(primary);
    background-color: color(bg-card);
    color: color(primary);
  }
}

.option-text {
  font-size: 14px;
  color: color(text-primary);
  flex: 1;
  line-height: 1.4;
}

// 确认答案按钮
.confirm-section {
  padding: 0 16px 20px;
  display: flex;
  justify-content: center;
}

// 答案解析
.answer-analysis {
  background-color: color(primary-light);
  padding: 15px;
  margin: 0 16px 20px;
  border-radius: 6px;
  position: relative;
}

.feedback-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.feedback-correct {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.feedback-wrong {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
}

.feedback-icon {
  font-size: 14px;
  font-weight: bold;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.feedback-correct .feedback-icon {
  color: #ffffff;
  background-color: #52c41a;
}

.feedback-wrong .feedback-icon {
  color: #ffffff;
  background-color: #ff4d4f;
}

.feedback-text {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.feedback-correct .feedback-text {
  color: #52c41a;
}

.feedback-wrong .feedback-text {
  color: #ff4d4f;
}

.analysis-title {
  font-size: 15px;
  color: color(primary);
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
}

.analysis-content,
.analysis-detail {
  font-size: 14px;
  color: color(text-primary);
  display: block;
  line-height: 1.5;
}

.analysis-content {
  margin-bottom: 5px;
}

// 导航栏
.navigation-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: color(bg-card);
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f4f4f4;
  height: 60px;
  z-index: 100;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 15px;
  border-radius: 20px;
  font-size: 14px;
  min-width: 100px;
  box-sizing: border-box;
  transition: all 0.2s ease;
  cursor: pointer;

  text {
    line-height: 1;
  }

  &__icon {
    display: flex;
    align-items: center;

    svg {
      width: 8px;
      height: 14px;
    }
  }

  &--prev {
    background-color: color(primary-light);
    border: 1px solid color(border-primary);
    color: color(primary);

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .nav-btn__icon {
      margin-right: 5px;
    }
  }

  &--list {
    background-color: color(bg-light);
    border: 1px solid color(border-light);
    color: color(text-secondary);
  }

  &--next {
    background-color: color(secondary);
    color: color(white);

    .nav-btn__icon {
      margin-left: 5px;
    }
  }

  &:hover {
    transform: translateY(-1px);
  }
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: color(bg-card);
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid color(border-light);
  }

  &__title {
    font-size: 18px;
    font-weight: 600;
    color: color(text-primary);
  }

  &__close {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: color(text-secondary);
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: color(text-primary);
    }
  }

  &__body {
    padding: 20px;
    max-height: calc(80vh - 80px);
    overflow-y: auto;
  }
}

.status-legend {
  margin-bottom: 24px;
}

.legend {
  &__title {
    font-size: 16px;
    font-weight: 600;
    color: color(text-primary);
    display: block;
    margin-bottom: 12px;
  }

  &__items {
    display: flex;
    gap: 20px;
  }

  &__item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;

    &--answered {
      background-color: color(success);
      border: 2px solid color(success);
    }

    &--current {
      background-color: color(primary);
      border: 2px solid color(primary);
    }
  }

  &__text {
    font-size: 14px;
    color: color(text-secondary);
  }
}

.questions-section {
  .section__title {
    font-size: 16px;
    font-weight: 600;
    color: color(text-primary);
    display: block;
    margin-bottom: 16px;
  }
}

.questions-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.question-grid-item {
  width: 48px;
  height: 48px;
  border: 2px solid color(border-medium);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: color(text-secondary);
  background-color: color(bg-card);
  cursor: pointer;
  transition: all 0.2s ease;

  &--answered {
    border-color: color(success);
    background-color: color(success-light);
    color: color(success);
  }

  &--current {
    border-color: color(primary);
    background-color: color(primary-light);
    color: color(primary);
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: color(text-secondary);
  padding: 20px;
}

.empty-text {
  font-size: 16px;
  color: color(text-secondary);
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: color(text-tertiary);
  text-align: center;
  margin-bottom: 20px;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

.back-btn {
  background-color: color(primary);
  color: color(white);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;

  &::after {
    border: none;
  }
}
</style>
