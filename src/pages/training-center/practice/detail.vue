<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getPracticeInfo } from '@/api/exam'
import type { Practice } from '@/types/api/Exam'
import { navigateTo } from '@uni-helper/uni-promises'

const practiceInfo = ref<Practice | null>(null)
const loading = ref(false)
const practiceId = ref<number>()

// 页面加载时获取参数
onLoad((options: any) => {
  if (options.practiceId) {
    practiceId.value = Number(options.practiceId)
    fetchPracticeInfo()
  }
})

// 获取练习基本信息
const fetchPracticeInfo = async () => {
  if (!practiceId.value) return

  try {
    loading.value = true
    const result = await getPracticeInfo({ practiceId: practiceId.value })
    practiceInfo.value = result
  } catch (error) {
    console.error('获取练习信息失败:', error)
    uni.showToast({
      title: '获取练习信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr?: string) => {
  if (!timeStr) return '--'
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 获取练习状态文本
const getStatusText = (status?: string) => {
  switch (status) {
    case 'IN_PROGRESS':
      return '进行中'
    case 'COMPLETED':
      return '已完成'
    default:
      return '未开始'
  }
}

// 获取练习状态样式
const getStatusClass = (status?: string) => {
  switch (status) {
    case 'IN_PROGRESS':
      return 'status-progress'
    case 'COMPLETED':
      return 'status-completed'
    default:
      return 'status-not-started'
  }
}

// 继续练习
const continuePractice = () => {
  if (practiceId.value) {
    navigateTo({
      url: `/pages/training-center/practice/practice?practiceId=${practiceId.value}`
    })
  }
}


</script>

<template>
  <view class="practice-detail-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 练习详情 -->
    <view v-else-if="practiceInfo" class="detail-content">
      <!-- 基本信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">练习信息</text>
          <view class="status-badge" :class="getStatusClass(practiceInfo.status)">
            <text class="status-text">{{ getStatusText(practiceInfo.status) }}</text>
          </view>
        </view>
        <view class="card-body">
          <view class="info-row">
            <text class="label">题库名称</text>
            <text class="value">{{ practiceInfo.bankNames || '--' }}</text>
          </view>
          <view class="info-row">
            <text class="label">练习ID</text>
            <text class="value">{{ practiceInfo.id }}</text>
          </view>
          <view class="info-row">
            <text class="label">开始时间</text>
            <text class="value">{{ formatTime(practiceInfo.startTime) }}</text>
          </view>
          <view class="info-row">
            <text class="label">最后更新</text>
            <text class="value">{{ formatTime(practiceInfo.lastUpdateTime) }}</text>
          </view>
          <view class="info-row">
            <text class="label">总题数</text>
            <text class="value">{{ practiceInfo.totalCount || 0 }}题</text>
          </view>
          <view class="info-row">
            <text class="label">单选题</text>
            <text class="value">{{ practiceInfo.singleChoiceCount || 0 }}题</text>
          </view>
          <view class="info-row">
            <text class="label">多选题</text>
            <text class="value">{{ practiceInfo.multipleChoiceCount || 0 }}题</text>
          </view>
        </view>
      </view>

      <!-- 练习进度卡片 -->
      <view class="progress-card">
        <view class="card-header">
          <text class="card-title">练习进度</text>
        </view>
        <view class="card-body">
          <view class="progress-stats">
            <view class="stat-item">
              <text class="stat-number">{{ practiceInfo.answeredCount || 0 }}</text>
              <text class="stat-label">已答题数</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-number">{{ (practiceInfo.totalCount || 0) - (practiceInfo.answeredCount || 0) }}</text>
              <text class="stat-label">剩余题数</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-number">{{ Math.round(((practiceInfo.answeredCount || 0) / (practiceInfo.totalCount || 1)) * 100) }}%</text>
              <text class="stat-label">完成进度</text>
            </view>
          </view>

          <!-- 进度条 -->
          <view class="progress-section">
            <view class="progress-info">
              <text class="progress-label">答题进度</text>
              <text class="progress-text">{{ practiceInfo.answeredCount || 0 }}/{{ practiceInfo.totalCount || 0 }}</text>
            </view>
            <view class="progress-bar">
              <view
                class="progress-fill"
                :style="{ width: `${((practiceInfo.answeredCount || 0) / (practiceInfo.totalCount || 1)) * 100}%` }"
              ></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="continue-btn" @click="continuePractice">
          {{ practiceInfo.status === 'COMPLETED' ? '查看练习' : '继续练习' }}
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-container">
      <text class="empty-text">练习详情不存在</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "练习详情"
  }
}
</route>

<style scoped lang="scss">
.practice-detail-page {
  background-color: #f7f7f5;
  min-height: 100vh;
  padding: 16px;
  padding-bottom: 80px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  color: #666;
  font-size: 14px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card, .progress-card {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.status-not-started {
  background-color: #f0f0f0;
  .status-text {
    color: #666;
  }
}

.status-progress {
  background-color: #e6f7ff;
  .status-text {
    color: #1890ff;
  }
}

.status-completed {
  background-color: #f6ffed;
  .status-text {
    color: #52c41a;
  }
}

.card-body {
  padding: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 14px;
  color: #666;
}

.value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-divider {
  width: 1px;
  height: 30px;
  background-color: #e9ecef;
}

.progress-section {
  margin-top: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: #666;
}

.progress-text {
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.progress-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #1890ff;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}

.continue-btn {
  width: 100%;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;

  &:active {
    background-color: #096dd9;
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.empty-text {
  font-size: 16px;
  color: #666;
}
</style>
