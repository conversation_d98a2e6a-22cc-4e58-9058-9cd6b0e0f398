<script setup lang="ts">
import type { ExamResult } from '@/types/api/Exam'
import { getExamDetail } from '@/api/exam'
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'

const toast = useToast()

const examDetail = ref<ExamResult | null>(null)
const examId = ref<number | null>(null)
const loading = ref(true)
const currentQuestionIndex = ref(0)
const showWrongOnly = ref(false)

// 过滤后的题目列表（如果只显示错题）
const filteredQuestions = computed(() => {
  if (!examDetail.value?.questions) return []

  if (showWrongOnly.value) {
    return examDetail.value.questions.filter(question => !isUserAnswerCorrect(question))
  }

  return examDetail.value.questions
})

// 当前题目
const currentQuestion = computed(() => {
  if (!filteredQuestions.value || filteredQuestions.value.length === 0) {
    return null
  }
  return filteredQuestions.value[currentQuestionIndex.value]
})

// 获取考试详情
const fetchExamDetail = async () => {
  if (!examId.value) {
    toast.error('考试ID不存在')
    return
  }

  try {
    loading.value = true
    const result = await getExamDetail({ examId: examId.value })

    if (!result) {
      toast.error('考试详情为空')
      return
    }

    if (!result.questions || result.questions.length === 0) {
      toast.error('考试题目为空')
      return
    }

    examDetail.value = result
  } catch (error) {
    console.error('获取考试详情失败:', error)
    toast.error('获取考试详情失败')
  } finally {
    loading.value = false
  }
}

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'SINGLE':
    case 'SINGLE_CHOICE':
      return '单选题'
    case 'MULTIPLE':
    case 'MULTIPLE_CHOICE':
      return '多选题'
    default:
      return type
  }
}

// 获取正确答案文本
const getCorrectOptionText = (question: any) => {
  if (!question || !question.answer || !question.options) return ''
  const correctOption = question.options.find((opt: any) => opt.optionKey === question.answer)
  return correctOption?.content ?? ''
}

// 检查用户答案是否正确
const isUserAnswerCorrect = (question: any) => {
  if (!question || !question.answer) return false

  const userAnswer = question.userAnswer
  if (!userAnswer) return false

  if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE') {
    if (!Array.isArray(userAnswer)) return false
    const sortedUserAnswer = userAnswer.sort().join(',')
    const sortedCorrectAnswer = question.answer.split(',').sort().join(',')
    return sortedUserAnswer === sortedCorrectAnswer
  } else {
    return userAnswer === question.answer
  }
}

// 检查选项是否被用户选中
const isOptionSelectedByUser = (question: any, optionKey: string) => {
  if (!question || !question.userAnswer) return false

  if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE') {
    return Array.isArray(question.userAnswer) && question.userAnswer.includes(optionKey)
  } else {
    return question.userAnswer === optionKey
  }
}

// 检查选项是否是正确答案
const isCorrectOption = (question: any, optionKey: string) => {
  if (!question || !question.answer) return false

  if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE') {
    return question.answer.split(',').includes(optionKey)
  } else {
    return question.answer === optionKey
  }
}

// 上一题
const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

// 下一题
const nextQuestion = () => {
  if (filteredQuestions.value && currentQuestionIndex.value < filteredQuestions.value.length - 1) {
    currentQuestionIndex.value++
  }
}

// 跳转到指定题目
const jumpToQuestion = (index: number) => {
  currentQuestionIndex.value = index
}

// 返回上一页
const handleGoBack = () => {
  uni.navigateBack()
}

onLoad((options: any) => {
  if (options.examId) {
    examId.value = Number(options.examId)
    showWrongOnly.value = options.showWrongOnly === 'true'
    fetchExamDetail()
  }
})
</script>

<template>
  <view class="exam-review-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 考试回顾内容 -->
    <view v-else-if="examDetail && currentQuestion" class="review-content">
      <!-- 进度信息 -->
      <view class="progress-section">
        <view class="progress-info">
          <view class="question-count">
            <text class="label">题目：</text>
            <text class="value">{{ currentQuestionIndex + 1 }}/{{ examDetail.questions.length }}</text>
          </view>
        </view>
      </view>

      <!-- 题目卡片 -->
      <view class="question-card">
        <view class="question-header">
          <text>问题 {{ currentQuestionIndex + 1 }}：{{ getQuestionTypeText(currentQuestion.type) }}</text>
        </view>

        <view class="question-content">
          <text>{{ currentQuestion.title }}</text>
        </view>

        <view class="options-list">
          <view
            v-for="option in currentQuestion?.options || []"
            :key="option.id"
            class="option"
            :class="{
              'user-selected': isOptionSelectedByUser(currentQuestion, option.optionKey),
              'correct-answer': isCorrectOption(currentQuestion, option.optionKey),
              'wrong-answer': isOptionSelectedByUser(currentQuestion, option.optionKey) && !isCorrectOption(currentQuestion, option.optionKey)
            }"
          >
            <view class="option-indicator">
              <text>{{ option.optionKey }}</text>
            </view>
            <text class="option-text">{{ option.content }}</text>
            <!-- 标记图标 -->
            <view class="option-marks">
              <text v-if="isCorrectOption(currentQuestion, option.optionKey)" class="correct-mark">✓</text>
              <text v-if="isOptionSelectedByUser(currentQuestion, option.optionKey) && !isCorrectOption(currentQuestion, option.optionKey)" class="wrong-mark">✗</text>
            </view>
          </view>
        </view>

        <!-- 答案解析 -->
        <view class="answer-analysis">
          <!-- 答题结果反馈 -->
          <view
            class="feedback-badge"
            :class="{
              'feedback-correct': isUserAnswerCorrect(currentQuestion),
              'feedback-wrong': !isUserAnswerCorrect(currentQuestion)
            }"
          >
            <text class="feedback-icon">
              {{ isUserAnswerCorrect(currentQuestion) ? '✓' : '✗' }}
            </text>
            <text class="feedback-text">
              {{ isUserAnswerCorrect(currentQuestion) ? '回答正确' : '回答错误' }}
            </text>
          </view>

          <text class="analysis-title">答案解析</text>
          <text class="analysis-content">正确答案：{{ currentQuestion.answer }} - {{ getCorrectOptionText(currentQuestion) }}</text>
          <text v-if="currentQuestion.analysis" class="analysis-detail">{{ currentQuestion.analysis }}</text>
        </view>
      </view>

      <!-- 底部导航 -->
      <view class="navigation-bar">
        <view class="nav-btn nav-btn--prev" :class="{ disabled: currentQuestionIndex === 0 }" @click="previousQuestion">
          <view class="nav-btn__icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="14" viewBox="0 0 8 14" fill="none">
              <path d="M7.14197 13.7985C6.98751 13.7978 6.83961 13.7359 6.73072 13.6264L0.230721 7.1264C0.00454944 6.89888 0.00454944 6.53143 0.230721 6.3039L6.73072 0.203903C6.95181 -0.0542651 7.34592 -0.0694876 7.58626 0.170858C7.82661 0.411202 7.81139 0.805313 7.55322 1.0264L1.46739 4.11515L7.55322 7.2039C7.71902 7.37069 7.76845 7.62073 7.67859 7.83806C7.58872 8.05539 7.37714 8.1975 7.14197 8.19849L7.14197 13.7985Z" fill="#1677FF"/>
            </svg>
          </view>
          <text>上一题</text>
        </view>

        <view class="nav-btn nav-btn--list" @click="handleGoBack">
          <text>返回</text>
        </view>

        <view class="nav-btn nav-btn--next" @click="nextQuestion" :class="{ disabled: currentQuestionIndex === examDetail.questions.length - 1 }">
          <text>下一题</text>
          <view v-if="currentQuestionIndex < examDetail.questions.length - 1" class="nav-btn__icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="14" viewBox="0 0 8 14" fill="none">
              <path d="M0.858031 13.7985C1.01249 13.7978 1.16039 13.7359 1.26928 13.6264L7.76928 7.1264C7.99545 6.89888 7.99545 6.53143 7.76928 6.3039L1.26928 0.203903C1.04819 -0.0542651 0.654082 -0.0694876 0.413736 0.170858C0.173391 0.411202 0.188613 0.805313 0.446784 1.0264L6.53261 4.11515L0.446784 7.2039C0.280983 7.37069 0.231553 7.62073 0.321417 7.83806C0.411282 8.05539 0.622863 8.1975 0.858031 8.19849L0.858031 13.7985Z" fill="#FFFFFF"/>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <text class="empty-text">考试内容不存在</text>
      <text class="empty-desc">请检查考试ID是否正确或联系管理员</text>
      <view class="empty-actions">
        <button class="back-btn" @click="handleGoBack">返回</button>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "考试回顾",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
// 设计令牌
$colors: (
  primary: #1677ff,
  primary-light: #f0f7ff,
  secondary: #37acfe,
  success: #52c41a,
  success-light: #f6ffed,
  error: #ff4d4f,
  error-light: #fff1f0,
  text-primary: #333333,
  text-secondary: #666666,
  text-tertiary: #999999,
  bg-page: #f7f7f5,
  bg-card: #ffffff,
  bg-light: #f5f7fa,
  border-light: #e8e8e8,
  border-medium: #d9d9d9,
  border-primary: #d6e8ff,
  white: #ffffff
);

@function color($name) {
  @return map-get($colors, $name);
}

// 基础布局
.exam-review-page {
  background-color: color(bg-page);
  min-height: 100vh;
  padding-bottom: 95px;
}

.review-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: color(text-secondary);
}

.loading-text {
  color: color(text-secondary);
  font-size: 14px;
}

// 进度区域
.progress-section {
  background-color: color(bg-card);
  padding: 12px 18px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: color(text-secondary);
}

.question-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.label {
  color: color(text-secondary);
}

.value {
  color: color(primary);
}

// 题目卡片
.question-card {
  background-color: color(bg-card);
  margin: 0 16px;
  border-radius: 8px;
  overflow: hidden;
}

.question-header {
  background-color: color(primary-light);
  padding: 8px 16px;
  font-size: 15px;
  color: color(primary);
  font-weight: 500;
}

.question-content {
  padding: 15px 16px;
  font-size: 15px;
  color: color(text-primary);
  line-height: 1.5;
}

.options-list {
  padding: 0 16px 20px;
}

.option {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid color(border-light);
  border-radius: 6px;
  margin-bottom: 10px;
  background-color: color(bg-card);
  position: relative;

  &.user-selected {
    border-color: color(primary);
    background-color: color(primary-light);
  }

  &.correct-answer {
    border-color: color(success);
    background-color: color(success-light);
  }

  &.wrong-answer {
    border-color: color(error);
    background-color: color(error-light);
  }
}

.option-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 1px solid color(border-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 14px;
  color: color(text-secondary);
}

.option-text {
  font-size: 14px;
  color: color(text-primary);
  flex: 1;
  line-height: 1.4;
}

.option-marks {
  display: flex;
  align-items: center;
  gap: 5px;
}

.correct-mark {
  color: color(success);
  font-size: 16px;
  font-weight: bold;
}

.wrong-mark {
  color: color(error);
  font-size: 16px;
  font-weight: bold;
}

// 答案解析
.answer-analysis {
  background-color: color(primary-light);
  padding: 15px;
  margin: 0 16px 20px;
  border-radius: 6px;
  position: relative;
}

.feedback-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.feedback-correct {
  background-color: color(success-light);
  border: 1px solid #b7eb8f;
}

.feedback-wrong {
  background-color: color(error-light);
  border: 1px solid #ffa39e;
}

.feedback-icon {
  font-size: 14px;
  font-weight: bold;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.feedback-correct .feedback-icon {
  color: color(white);
  background-color: color(success);
}

.feedback-wrong .feedback-icon {
  color: color(white);
  background-color: color(error);
}

.feedback-text {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.feedback-correct .feedback-text {
  color: color(success);
}

.feedback-wrong .feedback-text {
  color: color(error);
}

.analysis-title {
  font-size: 15px;
  color: color(primary);
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
}

.analysis-content,
.analysis-detail {
  font-size: 14px;
  color: color(text-primary);
  display: block;
  line-height: 1.5;
}

.analysis-content {
  margin-bottom: 5px;
}

// 导航栏
.navigation-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: color(bg-card);
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f4f4f4;
  height: 60px;
  z-index: 100;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 15px;
  border-radius: 20px;
  font-size: 14px;
  min-width: 100px;
  box-sizing: border-box;
  transition: all 0.2s ease;
  cursor: pointer;

  text {
    line-height: 1;
  }

  &__icon {
    display: flex;
    align-items: center;

    svg {
      width: 8px;
      height: 14px;
    }
  }

  &--prev {
    background-color: color(primary-light);
    border: 1px solid color(border-primary);
    color: color(primary);

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .nav-btn__icon {
      margin-right: 5px;
    }
  }

  &--list {
    background-color: color(bg-light);
    border: 1px solid color(border-light);
    color: color(text-secondary);
  }

  &--next {
    background-color: color(secondary);
    color: color(white);

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .nav-btn__icon {
      margin-left: 5px;
    }
  }

  &:hover:not(.disabled) {
    transform: translateY(-1px);
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: color(text-secondary);
  padding: 20px;
}

.empty-text {
  font-size: 16px;
  color: color(text-secondary);
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: color(text-tertiary);
  text-align: center;
  margin-bottom: 20px;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

.back-btn {
  background-color: color(primary);
  color: color(white);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;

  &::after {
    border: none;
  }
}
</style>
