
import type { PaginatedContent } from '@/service/types'
import type {
  CompletePracticeParams,
  DeletePracticeParams,
  Exam,
  ExamAnswer,
  ExamAnswerDetail,
  ExamAnswerRequest,
  ExamQuery,
  GeneratePracticeRequest,
  GetAnswerByExamAndUserParams,
  GetAnswerDetailParams,
  GetAnswersByExamParams,
  GetOrCreateAnswerParams,
  GetPracticeDetailParams,
  GetPracticeInfoParams,
  Practice,
  PracticeResult,
  QuestionBank,
  QuestionBankListParams,
  UpdatePracticeProgressRequest,
} from '@/types/api/Exam'

import { getInstance } from '@/service'
import { ContentType } from '@/service/types'
import { useUserStore } from '@/store'


/**
 * 分页查询当前用户考试列表
 * @param data - 查询参数
 */
export async function getExamUserPage(data: ExamQuery): Promise<PaginatedContent<Exam>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/user-page',
    merchant: '',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data)
}

/**
 * 根据考试ID和用户ID获取答卷
 * @param params - 查询参数
 */
export async function getAnswerByExamAndUser(
  params: GetAnswerByExamAndUserParams,
): Promise<ExamAnswer> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/getByExamAndUser',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 获取答卷详情
 * @param params - 查询参数
 */
export async function getAnswerDetail(params: GetAnswerDetailParams): Promise<ExamAnswerDetail> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/getDetail',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 获取或创建答卷
 * @param params - 查询参数
 */
export async function getOrCreateAnswer(params: GetOrCreateAnswerParams): Promise<ExamAnswer> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/getOrCreate',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 根据考试ID获取所有答卷
 * @param params - 查询参数
 */
export async function getAnswersByExam(params: GetAnswersByExamParams): Promise<ExamAnswer[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/listByExam',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 提交答案
 * @param data - 请求体
 */
export async function submitAnswer(data: ExamAnswerRequest): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/submit',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data)
}

/**
 * 更新答题进度
 * @param data - 请求体
 */
export async function updateAnswerProgress(data: ExamAnswerRequest): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/updateProgress',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data)
}

/**
 * 完成练习
 * @param params - 查询参数
 */
export async function completePractice(params: CompletePracticeParams): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/practice/complete',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, undefined, { params })
}

/**
 * 删除练习记录
 * @param params - 查询参数
 */
export async function deletePractice(params: DeletePracticeParams): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/practice/delete',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, undefined, { params })
}

/**
 * 获取练习详情
 * @param params - 查询参数
 */
export async function getPracticeDetail(params: GetPracticeDetailParams): Promise<PracticeResult> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/practice/detail',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 生成练习题目
 * @param data - 请求体
 */
export async function generatePracticeQuestions(
  data: GeneratePracticeRequest,
): Promise<PracticeResult> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/practice/generate',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data)
}

/**
 * 获取练习基本信息
 * @param params - 查询参数
 */
export async function getPracticeInfo(params: GetPracticeInfoParams): Promise<Practice> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/practice/info',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 获取用户练习记录列表
 */
export async function getUserPractices(): Promise<Practice[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/practice/list',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path)
}

/**
 * 更新练习答题进度
 * @param data - 请求数据
 */
export async function updatePracticeProgress(
  data: UpdatePracticeProgressRequest,
): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/practice/updateProgress',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data, {
    headers: {
      'Content-Type': ContentType.APPLICATION_X_WWW_FORM_URLENCODED,
    },
  })
}

/**
 * 获取题库列表
 * @param data - 查询参数
 */
export async function getQuestionBankList(data: QuestionBankListParams): Promise<QuestionBank[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/question/bank/list',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params: data })
}
